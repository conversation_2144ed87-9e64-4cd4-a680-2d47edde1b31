/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-10 06:54:54 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.pages.publisher;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class add_005fpublishers_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("    \r\n");
      out.write("        <html>\r\n");
      out.write("\r\n");
      out.write("        <head>\r\n");
      out.write("            <title>添加出版社信息</title>\r\n");
      out.write("        </head>\r\n");
      out.write("\r\n");
      out.write("        <body>\r\n");
      out.write("            <form method=\"post\" action=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/publisher/add_publishers\">\r\n");
      out.write("                <table width=\"100%\" border=\"1\" cellspacing=\"0\" cellpadding=\"0\">\r\n");
      out.write("                    <tbody>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td colspan=\"5\">添加出版社信息</td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td width=\"35\" align=\"center\">序号</td>\r\n");
      out.write("                            <td width=\"50\" align=\"center\">编号</td>\r\n");
      out.write("                            <td width=\"125\" align=\"center\">名称</td>\r\n");
      out.write("                            <td width=\"105\" align=\"center\">联系人</td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td align=\"center\">1</td>\r\n");
      out.write("                            <td><input name=\"publisherList[0].pubId\" type=\"text\" size=\"5\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[0].pubName\" type=\"text\" size=\"13\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[0].contacter\" type=\"text\" size=\"10\"></td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td align=\"center\">2</td>\r\n");
      out.write("                            <td><input name=\"publisherList[1].pubId\" type=\"text\" size=\"5\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[1].pubName\" type=\"text\" size=\"13\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[1].contacter\" type=\"text\" size=\"10\"></td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td align=\"center\">3</td>\r\n");
      out.write("                            <td><input name=\"publisherList[2].pubId\" type=\"text\" size=\"5\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[2].pubName\" type=\"text\" size=\"13\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[2].contacter\" type=\"text\" size=\"10\"></td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td align=\"center\">4</td>\r\n");
      out.write("                            <td><input name=\"publisherList[3].pubId\" type=\"text\" size=\"5\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[3].pubName\" type=\"text\" size=\"13\"></td>\r\n");
      out.write("                            <td><input name=\"publisherList[3].contacter\" type=\"text\" size=\"10\"></td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                        <tr>\r\n");
      out.write("                            <td colspan=\"5\" align=\"center\">\r\n");
      out.write("                                <input type=\"submit\" name=\"submit\" id=\"submit\" value=\"添加\">\r\n");
      out.write("                            </td>\r\n");
      out.write("                        </tr>\r\n");
      out.write("                    </tbody>\r\n");
      out.write("                </table>\r\n");
      out.write("            </form>\r\n");
      out.write("        </body>\r\n");
      out.write("\r\n");
      out.write("        </html>");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
