package com.javaee.ex07.converter;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.springframework.core.convert.converter.Converter;

public class DateConverter implements Converter<String, Date>
{

    @Override
    public Date convert(String source)
    {
        String           pattern          = "yyyy-MM-dd";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        try
        {
            return simpleDateFormat.parse(source);
        }
        catch (ParseException e)
        {
            throw new IllegalArgumentException("日期格式无效，请使用" + pattern + "格式");
        }
    }
}

